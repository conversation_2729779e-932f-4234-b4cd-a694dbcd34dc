package sirobilt.meghasanjivini.common.controller

import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import sirobilt.meghasanjivini.common.config.UpidConfiguration
import sirobilt.meghasanjivini.common.config.UpidFormatMetadata
import sirobilt.meghasanjivini.common.config.UpidFormatType
import sirobilt.meghasanjivini.common.service.UpidGeneratorService

/**
 * REST Controller for UPID (Unique Patient ID) configuration management
 */
@Path("/api/upid-config")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class UpidConfigurationController @Inject constructor(
    private val upidConfig: UpidConfiguration,
    private val upidGenerator: UpidGeneratorService
) {

    @GET
    @Path("/current")
    fun getCurrentConfiguration(): UpidConfigurationResponse {
        return UpidConfigurationResponse(
            formatType = upidConfig.format().type(),
            formatInfo = upidGenerator.getFormatInfo(),
            sequenceConfig = SequenceConfigResponse(
                global = upidConfig.sequence().global(),
                startFrom = upidConfig.sequence().startFrom(),
                resetAnnually = upidConfig.sequence().resetAnnually(),
                resetMonthly = upidConfig.sequence().resetMonthly()
            ),
            validationConfig = ValidationConfigResponse(
                checkDuplicates = upidConfig.validation().checkDuplicates(),
                allowManualOverride = upidConfig.validation().allowManualOverride(),
                reservedPatterns = upidConfig.validation().reservedPatterns().split(",")
            )
        )
    }

    @GET
    @Path("/formats")
    fun getAvailableFormats(): List<UpidFormatInfo> {
        return UpidFormatType.values().map { formatType ->
            val metadata = UpidFormatMetadata.getMetadata(formatType)
            UpidFormatInfo(
                type = formatType,
                description = metadata.description,
                example = metadata.example,
                pattern = metadata.pattern,
                maxLength = metadata.maxLength,
                minLength = metadata.minLength
            )
        }
    }

    @POST
    @Path("/generate-sample/{facilityId}")
    fun generateSampleUpid(
        @PathParam("facilityId") facilityId: String
    ): SampleUpidResponse {
        return try {
            val sampleUpid = upidGenerator.generateNextUpid(facilityId)
            SampleUpidResponse(
                success = true,
                upid = sampleUpid,
                facilityId = facilityId,
                formatType = upidConfig.format().type(),
                message = "Sample UPID generated successfully"
            )
        } catch (e: Exception) {
            SampleUpidResponse(
                success = false,
                upid = null,
                facilityId = facilityId,
                formatType = upidConfig.format().type(),
                message = "Failed to generate sample UPID: ${e.message}"
            )
        }
    }

    @POST
    @Path("/validate")
    fun validateUpid(request: UpidValidationRequest): UpidValidationResponse {
        return try {
            val isValid = upidGenerator.validateUpid(request.upid)
            UpidValidationResponse(
                valid = isValid,
                upid = request.upid,
                formatType = upidConfig.format().type(),
                message = if (isValid) "UPID is valid" else "UPID does not conform to current format"
            )
        } catch (e: Exception) {
            UpidValidationResponse(
                valid = false,
                upid = request.upid,
                formatType = upidConfig.format().type(),
                message = "Validation failed: ${e.message}"
            )
        }
    }

    @GET
    @Path("/health")
    fun healthCheck(): Response {
        return try {
            val formatType = upidConfig.format().type()
            val testUpid = upidGenerator.generateNextUpid("1")

            Response.ok(mapOf(
                "status" to "healthy",
                "formatType" to formatType.name,
                "testUpid" to testUpid,
                "timestamp" to System.currentTimeMillis()
            )).build()
        } catch (e: Exception) {
            Response.status(Response.Status.SERVICE_UNAVAILABLE)
                .entity(mapOf(
                    "status" to "unhealthy",
                    "error" to e.message,
                    "timestamp" to System.currentTimeMillis()
                )).build()
        }
    }
}

// DTOs for API responses
data class UpidConfigurationResponse(
    val formatType: UpidFormatType,
    val formatInfo: Map<String, Any>,
    val sequenceConfig: SequenceConfigResponse,
    val validationConfig: ValidationConfigResponse
)

data class SequenceConfigResponse(
    val global: Boolean,
    val startFrom: Long,
    val resetAnnually: Boolean,
    val resetMonthly: Boolean
)

data class ValidationConfigResponse(
    val checkDuplicates: Boolean,
    val allowManualOverride: Boolean,
    val reservedPatterns: List<String>
)

data class UpidFormatInfo(
    val type: UpidFormatType,
    val description: String,
    val example: String,
    val pattern: String?,
    val maxLength: Int?,
    val minLength: Int?
)

data class SampleUpidResponse(
    val success: Boolean,
    val upid: String?,
    val facilityId: String,
    val formatType: UpidFormatType,
    val message: String
)

data class UpidValidationRequest(
    val upid: String
)

data class UpidValidationResponse(
    val valid: Boolean,
    val upid: String,
    val formatType: UpidFormatType,
    val message: String
)
