package sirobilt.meghasanjivini.common.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.jboss.logging.Logger
import sirobilt.meghasanjivini.common.config.UpidConfiguration
import sirobilt.meghasanjivini.common.config.UpidFormatType
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * Service for generating Unique Patient IDs (UPID) based on configurable formats
 */
@ApplicationScoped
class UpidGeneratorService @Inject constructor(
    private val upidConfig: UpidConfiguration,
    private val patientRepository: PatientRepository
) {

    companion object {
        private val logger: Logger = Logger.getLogger(UpidGeneratorService::class.java)
    }

    /**
     * Generate next UPID based on configured format
     */
    fun generateNextUpid(facilityId: String): String {
        logger.info("Generating UPID for facility: $facilityId using format: ${upidConfig.format().type()}")
        
        return when (upidConfig.format().type()) {
            UpidFormatType.STANDARD -> generateStandardFormat(facilityId)
            UpidFormatType.CUSTOM -> generateCustomFormat(facilityId)
            UpidFormatType.NUMERIC -> generateNumericFormat(facilityId)
            UpidFormatType.ALPHANUMERIC -> generateAlphanumericFormat(facilityId)
            UpidFormatType.UUID -> generateUuidFormat(facilityId)
        }
    }

    /**
     * Generate standard format: 001-00-0000-0001
     */
    private fun generateStandardFormat(facilityId: String): String {
        val config = upidConfig.format().standard()
        val facilityNumber = facilityId.toInt()
        val paddedFacilityId = facilityNumber.toString().padStart(config.facilityDigits(), '0')
        val networkId = config.networkId().padStart(config.networkDigits(), '0')
        
        val sequence = getNextSequence()
        val paddedSequence = sequence.toString().padStart(config.sequenceDigits(), '0')
        val formattedSequence = formatSequenceWithSeparator(paddedSequence, config.separator())
        
        return "$paddedFacilityId${config.separator()}$networkId${config.separator()}$formattedSequence"
    }

    /**
     * Generate custom format using template: PAT{FACILITY}{YEAR}{SEQUENCE:6}
     */
    private fun generateCustomFormat(facilityId: String): String {
        val template = upidConfig.format().custom().template()
        val sequence = getNextSequence()
        val now = LocalDate.now()
        
        var result = template
            .replace("{FACILITY}", facilityId.padStart(3, '0'))
            .replace("{NETWORK}", upidConfig.format().standard().networkId())
            .replace("{YEAR}", now.year.toString())
            .replace("{MONTH}", now.monthValue.toString().padStart(2, '0'))
            .replace("{DAY}", now.dayOfMonth.toString().padStart(2, '0'))
        
        // Handle sequence with optional padding: {SEQUENCE:6}
        val sequenceRegex = "\\{SEQUENCE(?::(\\d+))?\\}".toRegex()
        result = sequenceRegex.replace(result) { matchResult ->
            val padding = matchResult.groupValues[1].toIntOrNull() ?: 1
            sequence.toString().padStart(padding, '0')
        }
        
        return result
    }

    /**
     * Generate numeric format: 1000000001
     */
    private fun generateNumericFormat(facilityId: String): String {
        val config = upidConfig.format().numeric()
        val sequence = getNextSequence()
        val prefix = config.prefix()
        val totalDigits = config.totalDigits()
        
        val numericPart = "$prefix$sequence"
        return numericPart.padStart(totalDigits, '0')
    }

    /**
     * Generate alphanumeric format: P001A0001
     */
    private fun generateAlphanumericFormat(facilityId: String): String {
        val config = upidConfig.format().alphanumeric()
        val facilityNumber = facilityId.toInt()
        val paddedFacilityId = facilityNumber.toString().padStart(config.facilityDigits(), '0')
        val sequence = getNextSequence()
        
        var suffix = config.suffixPattern()
        
        // Handle sequence placeholder in suffix: A{SEQUENCE:4}
        val sequenceRegex = "\\{SEQUENCE(?::(\\d+))?\\}".toRegex()
        suffix = sequenceRegex.replace(suffix) { matchResult ->
            val padding = matchResult.groupValues[1].toIntOrNull() ?: 1
            sequence.toString().padStart(padding, '0')
        }
        
        return "${config.prefix()}$paddedFacilityId$suffix"
    }

    /**
     * Generate UUID format: PAT-550e8400-e29b-41d4-a716-************
     */
    private fun generateUuidFormat(facilityId: String): String {
        val config = upidConfig.format().uuid()
        val uuid = UUID.randomUUID().toString()
        
        return if (config.includeFacility()) {
            "${config.prefix()}F$facilityId-$uuid"
        } else {
            "${config.prefix()}$uuid"
        }
    }

    /**
     * Get next sequence number based on configuration
     */
    private fun getNextSequence(): Long {
        val sequenceConfig = upidConfig.sequence()
        
        return if (sequenceConfig.global()) {
            getGlobalSequence()
        } else {
            // For facility-specific sequences, we would need facility parameter
            // For now, defaulting to global sequence
            getGlobalSequence()
        }
    }

    /**
     * Get next global sequence number
     */
    private fun getGlobalSequence(): Long {
        val lastUpid = patientRepository.findLastMrnGlobally()
        
        return if (lastUpid != null) {
            extractSequenceFromUpid(lastUpid) + 1
        } else {
            upidConfig.sequence().startFrom()
        }
    }

    /**
     * Extract sequence number from existing UPID based on current format
     */
    private fun extractSequenceFromUpid(upid: String): Long {
        return try {
            when (upidConfig.format().type()) {
                UpidFormatType.STANDARD -> {
                    // Extract from format: 001-00-0000-0001
                    val parts = upid.split(upidConfig.format().standard().separator())
                    if (parts.size >= 4) {
                        (parts[2] + parts[3]).toLongOrNull() ?: 0L
                    } else 0L
                }
                UpidFormatType.NUMERIC -> {
                    // Extract from numeric format
                    val prefix = upidConfig.format().numeric().prefix()
                    if (upid.startsWith(prefix)) {
                        upid.substring(prefix.length).toLongOrNull() ?: 0L
                    } else 0L
                }
                UpidFormatType.CUSTOM, UpidFormatType.ALPHANUMERIC -> {
                    // For custom and alphanumeric, try to extract trailing numbers
                    val numberRegex = "\\d+$".toRegex()
                    numberRegex.find(upid)?.value?.toLongOrNull() ?: 0L
                }
                UpidFormatType.UUID -> {
                    // UUID format doesn't have extractable sequence, start from config
                    upidConfig.sequence().startFrom()
                }
            }
        } catch (e: Exception) {
            logger.warn("Failed to extract sequence from UPID: $upid", e)
            upidConfig.sequence().startFrom()
        }
    }

    /**
     * Format sequence with separator for standard format
     */
    private fun formatSequenceWithSeparator(paddedSequence: String, separator: String): String {
        return if (paddedSequence.length >= 8) {
            "${paddedSequence.substring(0, 4)}$separator${paddedSequence.substring(4)}"
        } else {
            paddedSequence
        }
    }

    /**
     * Validate UPID format
     */
    fun validateUpid(upid: String): Boolean {
        if (upid.isBlank()) return false
        
        // Check reserved patterns
        val reservedPatterns = upidConfig.validation().reservedPatterns().split(",")
        if (reservedPatterns.any { upid.uppercase().contains(it.trim().uppercase()) }) {
            return false
        }
        
        // Check format-specific validation
        return when (upidConfig.format().type()) {
            UpidFormatType.STANDARD -> validateStandardFormat(upid)
            UpidFormatType.NUMERIC -> validateNumericFormat(upid)
            UpidFormatType.UUID -> validateUuidFormat(upid)
            else -> true // Custom and alphanumeric are more flexible
        }
    }

    private fun validateStandardFormat(upid: String): Boolean {
        val config = upidConfig.format().standard()
        val separator = config.separator()
        val parts = upid.split(separator)
        
        return parts.size == 4 &&
                parts[0].length == config.facilityDigits() &&
                parts[1].length == config.networkDigits() &&
                parts[2].length == 4 &&
                parts[3].length == 4 &&
                parts.all { it.all { char -> char.isDigit() } }
    }

    private fun validateNumericFormat(upid: String): Boolean {
        val config = upidConfig.format().numeric()
        return upid.length <= config.totalDigits() && upid.all { it.isDigit() }
    }

    private fun validateUuidFormat(upid: String): Boolean {
        val config = upidConfig.format().uuid()
        return upid.startsWith(config.prefix()) && 
               upid.length >= 36 // Minimum UUID length
    }

    /**
     * Get format information for API documentation
     */
    fun getFormatInfo(): Map<String, Any> {
        val formatType = upidConfig.format().type()
        return mapOf(
            "type" to formatType.name,
            "description" to getFormatDescription(formatType),
            "example" to getFormatExample(formatType),
            "configuration" to getFormatConfiguration()
        )
    }

    private fun getFormatDescription(type: UpidFormatType): String {
        return when (type) {
            UpidFormatType.STANDARD -> "Standard format with facility, network, and sequence"
            UpidFormatType.CUSTOM -> "Custom format with configurable template"
            UpidFormatType.NUMERIC -> "Pure numeric format"
            UpidFormatType.ALPHANUMERIC -> "Alphanumeric format with prefix and suffix"
            UpidFormatType.UUID -> "UUID format for maximum uniqueness"
        }
    }

    private fun getFormatExample(type: UpidFormatType): String {
        return when (type) {
            UpidFormatType.STANDARD -> "001-00-0000-0001"
            UpidFormatType.CUSTOM -> "PAT00120240001"
            UpidFormatType.NUMERIC -> "1000000001"
            UpidFormatType.ALPHANUMERIC -> "P001A0001"
            UpidFormatType.UUID -> "PAT-F1-550e8400-e29b-41d4-a716-************"
        }
    }

    private fun getFormatConfiguration(): Map<String, Any> {
        return mapOf(
            "global_sequence" to upidConfig.sequence().global(),
            "start_from" to upidConfig.sequence().startFrom(),
            "check_duplicates" to upidConfig.validation().checkDuplicates(),
            "allow_manual_override" to upidConfig.validation().allowManualOverride()
        )
    }
}
