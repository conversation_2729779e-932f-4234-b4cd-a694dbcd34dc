package sirobilt.meghasanjivini.common.config

import io.smallrye.config.ConfigMapping
import io.smallrye.config.WithDefault
import io.smallrye.config.WithName

/**
 * Configuration for UPID (Unique Patient ID) format and generation
 */
@ConfigMapping(prefix = "upid")
interface UpidConfiguration {
    
    /**
     * Format configuration
     */
    fun format(): FormatConfig
    
    /**
     * Sequence management configuration
     */
    fun sequence(): SequenceConfig
    
    /**
     * Validation configuration
     */
    fun validation(): ValidationConfig
    
    interface FormatConfig {
        /**
         * UPID format type: STANDARD, CUSTOM, NUMERIC, ALPHANUMERIC, UUID
         */
        @WithDefault("STANDARD")
        fun type(): UpidFormatType
        
        /**
         * Standard format configuration
         */
        fun standard(): StandardFormatConfig
        
        /**
         * Custom format configuration
         */
        fun custom(): CustomFormatConfig
        
        /**
         * Numeric format configuration
         */
        fun numeric(): NumericFormatConfig
        
        /**
         * Alphanumeric format configuration
         */
        fun alphanumeric(): AlphanumericFormatConfig
        
        /**
         * UUID format configuration
         */
        fun uuid(): UuidFormatConfig
    }
    
    interface StandardFormatConfig {
        @WithDefault("3")
        @WithName("facility-digits")
        fun facilityDigits(): Int
        
        @WithDefault("2")
        @WithName("network-digits")
        fun networkDigits(): Int
        
        @WithDefault("8")
        @WithName("sequence-digits")
        fun sequenceDigits(): Int
        
        @WithDefault("-")
        fun separator(): String
        
        @WithDefault("00")
        @WithName("network-id")
        fun networkId(): String
    }
    
    interface CustomFormatConfig {
        @WithDefault("PAT{FACILITY}{YEAR}{SEQUENCE:6}")
        fun template(): String
    }
    
    interface NumericFormatConfig {
        @WithDefault("1")
        fun prefix(): String
        
        @WithDefault("10")
        @WithName("total-digits")
        fun totalDigits(): Int
    }
    
    interface AlphanumericFormatConfig {
        @WithDefault("P")
        fun prefix(): String
        
        @WithDefault("3")
        @WithName("facility-digits")
        fun facilityDigits(): Int
        
        @WithDefault("A{SEQUENCE:4}")
        @WithName("suffix-pattern")
        fun suffixPattern(): String
    }
    
    interface UuidFormatConfig {
        @WithDefault("PAT-")
        fun prefix(): String
        
        @WithDefault("true")
        @WithName("include-facility")
        fun includeFacility(): Boolean
    }
    
    interface SequenceConfig {
        @WithDefault("true")
        fun global(): Boolean
        
        @WithDefault("1")
        @WithName("start-from")
        fun startFrom(): Long
        
        @WithDefault("false")
        @WithName("reset-annually")
        fun resetAnnually(): Boolean
        
        @WithDefault("false")
        @WithName("reset-monthly")
        fun resetMonthly(): Boolean
    }
    
    interface ValidationConfig {
        @WithDefault("true")
        @WithName("check-duplicates")
        fun checkDuplicates(): Boolean
        
        @WithDefault("false")
        @WithName("allow-manual-override")
        fun allowManualOverride(): Boolean
        
        @WithDefault("ADMIN,TEST,DEMO")
        @WithName("reserved-patterns")
        fun reservedPatterns(): String
    }
}

/**
 * UPID format types
 */
enum class UpidFormatType {
    /**
     * Standard format: 001-00-0000-0001
     */
    STANDARD,
    
    /**
     * Custom format with placeholders: PAT{FACILITY}{YEAR}{SEQUENCE:6}
     */
    CUSTOM,
    
    /**
     * Pure numeric format: 1000000001
     */
    NUMERIC,
    
    /**
     * Alphanumeric format: P001A0001
     */
    ALPHANUMERIC,
    
    /**
     * UUID format: PAT-550e8400-e29b-41d4-a716-************
     */
    UUID
}

/**
 * UPID format metadata for documentation and validation
 */
data class UpidFormatMetadata(
    val type: UpidFormatType,
    val description: String,
    val example: String,
    val pattern: String? = null,
    val maxLength: Int? = null,
    val minLength: Int? = null
) {
    companion object {
        fun forType(type: UpidFormatType): UpidFormatMetadata {
            return when (type) {
                UpidFormatType.STANDARD -> UpidFormatMetadata(
                    type = UpidFormatType.STANDARD,
                    description = "Standard format with facility, network, and sequence",
                    example = "001-00-0000-0001",
                    pattern = "^\\d{3}-\\d{2}-\\d{4}-\\d{4}$",
                    maxLength = 15,
                    minLength = 15
                )
                UpidFormatType.CUSTOM -> UpidFormatMetadata(
                    type = UpidFormatType.CUSTOM,
                    description = "Custom format with configurable template",
                    example = "PAT00120240001",
                    maxLength = 50,
                    minLength = 5
                )
                UpidFormatType.NUMERIC -> UpidFormatMetadata(
                    type = UpidFormatType.NUMERIC,
                    description = "Pure numeric format",
                    example = "1000000001",
                    pattern = "^\\d+$",
                    maxLength = 20,
                    minLength = 5
                )
                UpidFormatType.ALPHANUMERIC -> UpidFormatMetadata(
                    type = UpidFormatType.ALPHANUMERIC,
                    description = "Alphanumeric format with prefix and suffix",
                    example = "P001A0001",
                    pattern = "^[A-Z]\\d{3}[A-Z]\\d{4}$",
                    maxLength = 15,
                    minLength = 5
                )
                UpidFormatType.UUID -> UpidFormatMetadata(
                    type = UpidFormatType.UUID,
                    description = "UUID format for maximum uniqueness",
                    example = "PAT-550e8400-e29b-41d4-a716-************",
                    pattern = "^[A-Z]+-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                    maxLength = 50,
                    minLength = 36
                )
            }
        }
    }
}
